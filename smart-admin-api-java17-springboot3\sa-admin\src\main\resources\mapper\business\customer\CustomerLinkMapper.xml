<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.lab1024.sa.admin.module.business.customer.dao.CustomerLinkDao">

    <select id="queryAnalysis" resultType="net.lab1024.sa.admin.module.business.customer.domain.vo.CustomerLinkAnalysisVO">
        WITH
        -- CTE 1: 筛选付款期间的目标客户（与queryDetail保持一致）
        TargetCustomers AS (
            SELECT DISTINCT 客户唯一编码
            FROM lirun.订单明细 om
            <where>
                om.平台货品ID IS NOT NULL AND om.平台货品ID != ''
                AND om.客户唯一编码 IS NOT NULL AND om.客户唯一编码 != ''
                AND om.付款时间 IS NOT NULL
                AND om.订单状态 != '线下退款'
                AND NOT (om.订单来源 = '手工创建' AND om.订单状态 = '已取消')
                AND om.标记名称 != '贴贴贴贴贴'
                <if test="queryForm.excludeFlag != null and queryForm.excludeFlag != ''">
                    AND (om.客服标旗 IS NULL OR om.客服标旗 NOT IN
                    <foreach collection="queryForm.excludeFlag.split(',')" item="flag" open="(" separator="," close=")">
                        #{flag}
                    </foreach>)
                </if>
                <if test="queryForm.platformGoodsIds != null and queryForm.platformGoodsIds.size() > 0">
                    AND om.平台货品ID IN
                    <foreach collection="queryForm.platformGoodsIds" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="queryForm.startDate != null and queryForm.startDate != ''">
                    AND om.付款时间 &gt;= #{queryForm.startDate}
                </if>
                <if test="queryForm.endDate != null and queryForm.endDate != ''">
                    AND om.付款时间 &lt; DATE_ADD(#{queryForm.endDate}, INTERVAL 1 DAY)
                </if>
            </where>
        ),

        -- CTE 2: 判断是否为跨时间段复购分析（与queryDetail保持一致）
        CrossPeriodAnalysis AS (
            SELECT
                CASE
                    WHEN #{queryForm.startDate} = #{queryForm.repurchaseDateBegin}
                         AND #{queryForm.endDate} = #{queryForm.repurchaseDateEnd}
                    THEN 0  -- 同时间段分析
                    ELSE 1  -- 跨时间段分析
                END as is_cross_period
        ),

        -- CTE 3: 根据分析类型筛选复购客户（与queryDetail保持一致）
        RepurchaseCustomers AS (
            -- 跨时间段分析：在付款期间购买过 + 在回购期间也购买过的客户
            SELECT DISTINCT om.客户唯一编码
            FROM lirun.订单明细 om
            JOIN TargetCustomers tc ON om.客户唯一编码 = tc.客户唯一编码
            CROSS JOIN CrossPeriodAnalysis cpa
            WHERE cpa.is_cross_period = 1
                AND om.平台货品ID IS NOT NULL AND om.平台货品ID != ''
                AND om.客户唯一编码 IS NOT NULL AND om.客户唯一编码 != ''
                AND om.付款时间 IS NOT NULL
                AND om.订单状态 != '线下退款'
                AND NOT (om.订单来源 = '手工创建' AND om.订单状态 = '已取消')
                AND om.标记名称 != '贴贴贴贴贴'
                <if test="queryForm.excludeFlag != null and queryForm.excludeFlag != ''">
                    AND (om.客服标旗 IS NULL OR om.客服标旗 NOT IN
                    <foreach collection="queryForm.excludeFlag.split(',')" item="flag" open="(" separator="," close=")">
                        #{flag}
                    </foreach>)
                </if>
                <if test="queryForm.platformGoodsIds != null and queryForm.platformGoodsIds.size() > 0">
                    AND om.平台货品ID IN
                    <foreach collection="queryForm.platformGoodsIds" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="queryForm.repurchaseDateBegin != null and queryForm.repurchaseDateBegin != ''">
                    AND om.付款时间 &gt;= #{queryForm.repurchaseDateBegin}
                </if>
                <if test="queryForm.repurchaseDateEnd != null and queryForm.repurchaseDateEnd != ''">
                    AND om.付款时间 &lt; DATE_ADD(#{queryForm.repurchaseDateEnd}, INTERVAL 1 DAY)
                </if>

            UNION

            -- 同时间段分析：在该时间段内购买次数≥2的客户
            SELECT 客户唯一编码
            FROM (
                SELECT
                    tc.客户唯一编码,
                    COUNT(DISTINCT DATE(om.付款时间)) as purchase_days
                FROM TargetCustomers tc
                JOIN lirun.订单明细 om ON tc.客户唯一编码 = om.客户唯一编码
                CROSS JOIN CrossPeriodAnalysis cpa
                WHERE cpa.is_cross_period = 0
                    AND om.平台货品ID IS NOT NULL AND om.平台货品ID != ''
                    AND om.订单状态 != '线下退款'
                    AND NOT (om.订单来源 = '手工创建' AND om.订单状态 = '已取消')
                    AND om.标记名称 != '贴贴贴贴贴'
                    <if test="queryForm.excludeFlag != null and queryForm.excludeFlag != ''">
                        AND (om.客服标旗 IS NULL OR om.客服标旗 NOT IN
                        <foreach collection="queryForm.excludeFlag.split(',')" item="flag" open="(" separator="," close=")">
                            #{flag}
                        </foreach>)
                    </if>
                    <if test="queryForm.platformGoodsIds != null and queryForm.platformGoodsIds.size() > 0">
                        AND om.平台货品ID IN
                        <foreach collection="queryForm.platformGoodsIds" item="item" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                    </if>
                    <if test="queryForm.startDate != null and queryForm.startDate != ''">
                        AND om.付款时间 &gt;= #{queryForm.startDate}
                    </if>
                    <if test="queryForm.endDate != null and queryForm.endDate != ''">
                        AND om.付款时间 &lt; DATE_ADD(#{queryForm.endDate}, INTERVAL 1 DAY)
                    </if>
                GROUP BY tc.客户唯一编码
                HAVING purchase_days >= 2
            ) same_period_repurchase
        ),

        -- CTE 4: 应用客户类型筛选
        FilteredRepurchaseCustomers AS (
            SELECT rc.客户唯一编码
            FROM RepurchaseCustomers rc
            <if test="queryForm.customerType != null and queryForm.customerType != ''">
            WHERE CASE
                WHEN EXISTS (
                    SELECT 1 FROM lirun.订单明细 hist
                    WHERE hist.客户唯一编码 = rc.客户唯一编码
                        AND hist.付款时间 &lt; #{queryForm.startDate}
                        AND hist.订单状态 != '线下退款'
                        AND NOT (hist.订单来源 = '手工创建' AND hist.订单状态 = '已取消')
                        AND hist.标记名称 != '贴贴贴贴贴'
                ) THEN '老客'
                ELSE '新客'
            END = #{queryForm.customerType}
            </if>
        ),

        -- CTE 5: 计算复购周期相关数据（基于回购期间的购买数据）
        RepurchaseCycleData AS (
            SELECT
                frc.客户唯一编码,
                om.付款时间,
                DATE(om.付款时间) as 付款日期
            FROM FilteredRepurchaseCustomers frc
            JOIN lirun.订单明细 om ON frc.客户唯一编码 = om.客户唯一编码
            WHERE om.平台货品ID IS NOT NULL AND om.平台货品ID != ''
                AND om.订单状态 != '线下退款'
                AND NOT (om.订单来源 = '手工创建' AND om.订单状态 = '已取消')
                AND om.标记名称 != '贴贴贴贴贴'
                <if test="queryForm.platformGoodsIds != null and queryForm.platformGoodsIds.size() > 0">
                    AND om.平台货品ID IN
                    <foreach collection="queryForm.platformGoodsIds" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="queryForm.repurchaseDateBegin != null and queryForm.repurchaseDateBegin != ''">
                    AND om.付款时间 &gt;= #{queryForm.repurchaseDateBegin}
                </if>
                <if test="queryForm.repurchaseDateEnd != null and queryForm.repurchaseDateEnd != ''">
                    AND om.付款时间 &lt; DATE_ADD(#{queryForm.repurchaseDateEnd}, INTERVAL 1 DAY)
                </if>
        ),

        -- CTE 6: 计算购买间隔
        RepurchaseIntervals AS (
            SELECT
                客户唯一编码,
                付款日期,
                LAG(付款日期, 1) OVER (PARTITION BY 客户唯一编码 ORDER BY 付款日期) as previous_date,
                DATEDIFF(付款日期, LAG(付款日期, 1) OVER (PARTITION BY 客户唯一编码 ORDER BY 付款日期)) as interval_days
            FROM (
                SELECT DISTINCT 客户唯一编码, 付款日期
                FROM RepurchaseCycleData
            ) distinct_dates
        ),

        -- CTE 7: 计算间隔统计
        IntervalStats AS (
            SELECT
                客户唯一编码,
                MIN(interval_days) as 客户最小间隔,
                MAX(interval_days) as 客户最大间隔,
                AVG(interval_days) as 客户平均间隔
            FROM RepurchaseIntervals
            WHERE interval_days IS NOT NULL
            GROUP BY 客户唯一编码
        )

        -- 【最终步：聚合结果】（与queryDetail保持一致的复购逻辑）
        SELECT
            -- 目标客户人数：在付款日期范围内购买了指定货品的客户总数
            (SELECT COUNT(*) FROM TargetCustomers) as paymentUserCount,
            -- 复购人数：目标客户中在回购日期范围内也有购买行为的客户数
            (SELECT COUNT(*) FROM FilteredRepurchaseCustomers) as repurchaseUserCount,
            -- 验证后的复购人数（与repurchaseUserCount相同，保持兼容性）
            (SELECT COUNT(*) FROM FilteredRepurchaseCustomers) as validatedRepurchaseUserCount,
            -- 复购率：复购人数 / 目标客户人数 * 100（使用截断而非四舍五入）
            TRUNCATE((SELECT COUNT(*) FROM FilteredRepurchaseCustomers) * 100.0 / (SELECT COUNT(*) FROM TargetCustomers), 2) as repurchaseRate,
            -- 验证后的复购率（与repurchaseRate相同，保持兼容性）
            TRUNCATE((SELECT COUNT(*) FROM FilteredRepurchaseCustomers) * 100.0 / (SELECT COUNT(*) FROM TargetCustomers), 2) as validatedRepurchaseRate,
            -- 平均复购周期：从付款期间首次购买到回购期间最后购买的平均天数
            ROUND(AVG(DATEDIFF(
                (SELECT MAX(DATE(om2.付款时间))
                 FROM lirun.订单明细 om2
                 WHERE om2.客户唯一编码 = frc.客户唯一编码
                   AND om2.平台货品ID IN
                   <foreach collection="queryForm.platformGoodsIds" item="item" open="(" separator="," close=")">
                       #{item}
                   </foreach>
                   <if test="queryForm.repurchaseDateBegin != null and queryForm.repurchaseDateBegin != ''">
                       AND om2.付款时间 &gt;= #{queryForm.repurchaseDateBegin}
                   </if>
                   <if test="queryForm.repurchaseDateEnd != null and queryForm.repurchaseDateEnd != ''">
                       AND om2.付款时间 &lt; DATE_ADD(#{queryForm.repurchaseDateEnd}, INTERVAL 1 DAY)
                   </if>
                   AND om2.订单状态 != '线下退款'
                   AND NOT (om2.订单来源 = '手工创建' AND om2.订单状态 = '已取消')
                   AND om2.标记名称 != '贴贴贴贴贴'),
                (SELECT MIN(DATE(om1.付款时间))
                 FROM lirun.订单明细 om1
                 WHERE om1.客户唯一编码 = frc.客户唯一编码
                   AND om1.平台货品ID IN
                   <foreach collection="queryForm.platformGoodsIds" item="item" open="(" separator="," close=")">
                       #{item}
                   </foreach>
                   <if test="queryForm.startDate != null and queryForm.startDate != ''">
                       AND om1.付款时间 &gt;= #{queryForm.startDate}
                   </if>
                   <if test="queryForm.endDate != null and queryForm.endDate != ''">
                       AND om1.付款时间 &lt; DATE_ADD(#{queryForm.endDate}, INTERVAL 1 DAY)
                   </if>
                   AND om1.订单状态 != '线下退款'
                   AND NOT (om1.订单来源 = '手工创建' AND om1.订单状态 = '已取消')
                   AND om1.标记名称 != '贴贴贴贴贴')
            )), 2) as avgRepurchaseCycle,
            -- 最小、最大、平均复购间隔
            MIN(i.客户最小间隔) as minRepurchaseInterval,
            MAX(i.客户最大间隔) as maxRepurchaseInterval,
            ROUND(AVG(i.客户平均间隔), 2) as avgRepurchaseInterval,
            -- 目标客户人数（重复字段，保持兼容性）
            (SELECT COUNT(*) FROM TargetCustomers) as targetCustomerCount,
            -- 目标复购人数（与repurchaseUserCount相同，保持兼容性）
            (SELECT COUNT(*) FROM FilteredRepurchaseCustomers) as targetRepurchaseUserCount
        FROM FilteredRepurchaseCustomers frc
        LEFT JOIN IntervalStats i ON frc.客户唯一编码 = i.客户唯一编码
    </select>



    <select id="queryDetail" resultType="net.lab1024.sa.admin.module.business.customer.domain.vo.CustomerLinkDetailVO">
        -- 链接复购率次数分布分析（修正版 - 正确的复购逻辑）
        -- 正确逻辑：先找付款期间的目标客户，再看他们在回购期间的购买行为
        WITH
        -- CTE 1: 筛选付款期间的目标客户（基础客户群体）
        TargetCustomers AS (
            SELECT DISTINCT om.客户唯一编码
            FROM lirun.订单明细 om
            <where>
                om.平台货品ID IS NOT NULL AND om.平台货品ID != ''
                AND om.客户唯一编码 IS NOT NULL AND om.客户唯一编码 != ''
                AND om.付款时间 IS NOT NULL
                AND om.订单状态 != '线下退款'
                AND NOT (om.订单来源 = '手工创建' AND om.订单状态 = '已取消')
                AND om.标记名称 != '贴贴贴贴贴'
                <if test="queryForm.excludeFlag != null and queryForm.excludeFlag != ''">
                    AND (om.客服标旗 IS NULL OR om.客服标旗 NOT IN
                    <foreach collection="queryForm.excludeFlag.split(',')" item="flag" open="(" separator="," close=")">
                        #{flag}
                    </foreach>)
                </if>
                <if test="queryForm.platformGoodsIds != null and queryForm.platformGoodsIds.size() > 0">
                    AND om.平台货品ID IN
                    <foreach collection="queryForm.platformGoodsIds" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="queryForm.startDate != null and queryForm.startDate != ''">
                    AND om.付款时间 &gt;= #{queryForm.startDate}
                </if>
                <if test="queryForm.endDate != null and queryForm.endDate != ''">
                    AND om.付款时间 &lt; DATE_ADD(#{queryForm.endDate}, INTERVAL 1 DAY)
                </if>
            </where>
        ),

        -- CTE 2: 判断是否为跨时间段复购分析
        CrossPeriodAnalysis AS (
            SELECT
                CASE
                    WHEN #{queryForm.startDate} = #{queryForm.repurchaseDateBegin}
                         AND #{queryForm.endDate} = #{queryForm.repurchaseDateEnd}
                    THEN 0  -- 同时间段分析
                    ELSE 1  -- 跨时间段分析
                END as is_cross_period
        ),

        -- CTE 3: 根据分析类型筛选复购客户
        RepurchaseCustomers AS (
            -- 跨时间段分析：在付款期间购买过 + 在回购期间也购买过的客户
            SELECT DISTINCT om.客户唯一编码
            FROM lirun.订单明细 om
            JOIN TargetCustomers tc ON om.客户唯一编码 = tc.客户唯一编码
            CROSS JOIN CrossPeriodAnalysis cpa
            WHERE cpa.is_cross_period = 1
                AND om.平台货品ID IS NOT NULL AND om.平台货品ID != ''
                AND om.客户唯一编码 IS NOT NULL AND om.客户唯一编码 != ''
                AND om.付款时间 IS NOT NULL
                AND om.订单状态 != '线下退款'
                AND NOT (om.订单来源 = '手工创建' AND om.订单状态 = '已取消')
                AND om.标记名称 != '贴贴贴贴贴'
                <if test="queryForm.excludeFlag != null and queryForm.excludeFlag != ''">
                    AND (om.客服标旗 IS NULL OR om.客服标旗 NOT IN
                    <foreach collection="queryForm.excludeFlag.split(',')" item="flag" open="(" separator="," close=")">
                        #{flag}
                    </foreach>)
                </if>
                <if test="queryForm.platformGoodsIds != null and queryForm.platformGoodsIds.size() > 0">
                    AND om.平台货品ID IN
                    <foreach collection="queryForm.platformGoodsIds" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="queryForm.repurchaseDateBegin != null and queryForm.repurchaseDateBegin != ''">
                    AND om.付款时间 &gt;= #{queryForm.repurchaseDateBegin}
                </if>
                <if test="queryForm.repurchaseDateEnd != null and queryForm.repurchaseDateEnd != ''">
                    AND om.付款时间 &lt; DATE_ADD(#{queryForm.repurchaseDateEnd}, INTERVAL 1 DAY)
                </if>

            UNION

            -- 同时间段分析：在该时间段内购买次数≥2的客户
            SELECT 客户唯一编码
            FROM (
                SELECT
                    tc.客户唯一编码,
                    COUNT(DISTINCT DATE(om.付款时间)) as purchase_days
                FROM TargetCustomers tc
                JOIN lirun.订单明细 om ON tc.客户唯一编码 = om.客户唯一编码
                CROSS JOIN CrossPeriodAnalysis cpa
                WHERE cpa.is_cross_period = 0
                    AND om.平台货品ID IS NOT NULL AND om.平台货品ID != ''
                    AND om.订单状态 != '线下退款'
                    AND NOT (om.订单来源 = '手工创建' AND om.订单状态 = '已取消')
                    AND om.标记名称 != '贴贴贴贴贴'
                    <if test="queryForm.excludeFlag != null and queryForm.excludeFlag != ''">
                        AND (om.客服标旗 IS NULL OR om.客服标旗 NOT IN
                        <foreach collection="queryForm.excludeFlag.split(',')" item="flag" open="(" separator="," close=")">
                            #{flag}
                        </foreach>)
                    </if>
                    <if test="queryForm.platformGoodsIds != null and queryForm.platformGoodsIds.size() > 0">
                        AND om.平台货品ID IN
                        <foreach collection="queryForm.platformGoodsIds" item="item" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                    </if>
                    <if test="queryForm.startDate != null and queryForm.startDate != ''">
                        AND om.付款时间 &gt;= #{queryForm.startDate}
                    </if>
                    <if test="queryForm.endDate != null and queryForm.endDate != ''">
                        AND om.付款时间 &lt; DATE_ADD(#{queryForm.endDate}, INTERVAL 1 DAY)
                    </if>
                GROUP BY tc.客户唯一编码
                HAVING purchase_days >= 2
            ) same_period_repurchase
        ),

        -- CTE 4: 应用客户类型筛选
        FilteredRepurchaseCustomers AS (
            SELECT rc.客户唯一编码
            FROM RepurchaseCustomers rc
            <if test="queryForm.customerType != null and queryForm.customerType != ''">
            WHERE CASE
                WHEN EXISTS (
                    SELECT 1 FROM lirun.订单明细 hist
                    WHERE hist.客户唯一编码 = rc.客户唯一编码
                        AND hist.付款时间 &lt; #{queryForm.startDate}
                        AND hist.订单状态 != '线下退款'
                        AND NOT (hist.订单来源 = '手工创建' AND hist.订单状态 = '已取消')
                        AND hist.标记名称 != '贴贴贴贴贴'
                ) THEN '老客'
                ELSE '新客'
            END = #{queryForm.customerType}
            </if>
        ),

        -- CTE 5: 获取复购客户的购买数据（根据分析类型选择不同的时间范围）
        RepurchaseOrders AS (
            SELECT
                frc.客户唯一编码,
                om.数量,
                om.已付,
                DATE(om.付款时间) as 付款日期
            FROM FilteredRepurchaseCustomers frc
            JOIN lirun.订单明细 om ON frc.客户唯一编码 = om.客户唯一编码
            CROSS JOIN CrossPeriodAnalysis cpa
            WHERE om.平台货品ID IS NOT NULL AND om.平台货品ID != ''
                AND om.订单状态 != '线下退款'
                AND NOT (om.订单来源 = '手工创建' AND om.订单状态 = '已取消')
                AND om.标记名称 != '贴贴贴贴贴'
                <if test="queryForm.excludeFlag != null and queryForm.excludeFlag != ''">
                    AND (om.客服标旗 IS NULL OR om.客服标旗 NOT IN
                    <foreach collection="queryForm.excludeFlag.split(',')" item="flag" open="(" separator="," close=")">
                        #{flag}
                    </foreach>)
                </if>
                <if test="queryForm.platformGoodsIds != null and queryForm.platformGoodsIds.size() > 0">
                    AND om.平台货品ID IN
                    <foreach collection="queryForm.platformGoodsIds" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                AND (
                    -- 跨时间段分析：使用回购日期范围
                    (cpa.is_cross_period = 1
                     <if test="queryForm.repurchaseDateBegin != null and queryForm.repurchaseDateBegin != ''">
                         AND om.付款时间 &gt;= #{queryForm.repurchaseDateBegin}
                     </if>
                     <if test="queryForm.repurchaseDateEnd != null and queryForm.repurchaseDateEnd != ''">
                         AND om.付款时间 &lt; DATE_ADD(#{queryForm.repurchaseDateEnd}, INTERVAL 1 DAY)
                     </if>
                    )
                    OR
                    -- 同时间段分析：使用付款日期范围
                    (cpa.is_cross_period = 0
                     <if test="queryForm.startDate != null and queryForm.startDate != ''">
                         AND om.付款时间 &gt;= #{queryForm.startDate}
                     </if>
                     <if test="queryForm.endDate != null and queryForm.endDate != ''">
                         AND om.付款时间 &lt; DATE_ADD(#{queryForm.endDate}, INTERVAL 1 DAY)
                     </if>
                    )
                )
        ),
        -- CTE 6: 按天聚合回购期间的购买数据
        DailyRepurchaseData AS (
            SELECT 客户唯一编码, 付款日期, SUM(数量) as daily_quantity, SUM(已付) as daily_paid
            FROM RepurchaseOrders
            GROUP BY 客户唯一编码, 付款日期
        ),

        -- CTE 7: 计算每个复购客户在回购期间的购买次序和间隔
        RepurchaseSequence AS (
            SELECT
                drd.客户唯一编码,
                drd.付款日期,
                drd.daily_quantity,
                drd.daily_paid,
                ROW_NUMBER() OVER (PARTITION BY drd.客户唯一编码 ORDER BY drd.付款日期) as purchase_order,
                LAG(drd.付款日期, 1) OVER (PARTITION BY drd.客户唯一编码 ORDER BY drd.付款日期) as previous_purchase_date
            FROM DailyRepurchaseData drd
            JOIN FilteredRepurchaseCustomers frc ON drd.客户唯一编码 = frc.客户唯一编码
        ),

        -- CTE 8: 计算购买间隔
        RepurchaseWithInterval AS (
            SELECT
                客户唯一编码,
                purchase_order,
                daily_quantity,
                daily_paid,
                CASE
                    WHEN previous_purchase_date IS NOT NULL
                    THEN DATEDIFF(付款日期, previous_purchase_date)
                    ELSE NULL
                END as interval_days
            FROM RepurchaseSequence
        ),

        -- CTE 9: 计算每个客户的总购买次数
        CustomerTotalPurchases AS (
            SELECT
                rwi.客户唯一编码,
                MAX(rwi.purchase_order) as total_purchase_times,
                SUM(rwi.daily_quantity) as customer_total_quantity,
                SUM(rwi.daily_paid) as customer_total_amount,
                AVG(rwi.interval_days) as customer_avg_interval
            FROM RepurchaseWithInterval rwi
            GROUP BY rwi.客户唯一编码
        ),

        -- CTE 10: 根据分析类型过滤客户（分离过滤逻辑以避免HAVING子句中的CROSS JOIN问题）
        FilteredCustomerTotalPurchases AS (
            SELECT
                ctp.客户唯一编码,
                ctp.total_purchase_times,
                ctp.customer_total_quantity,
                ctp.customer_total_amount,
                ctp.customer_avg_interval
            FROM CustomerTotalPurchases ctp
            CROSS JOIN CrossPeriodAnalysis cpa
            WHERE (
                -- 跨时间段分析：所有复购客户都有效（因为已经通过RepurchaseCustomers筛选过了）
                (cpa.is_cross_period = 1)
                OR
                -- 同时间段分析：只保留购买次数>=2的客户
                (cpa.is_cross_period = 0 AND ctp.total_purchase_times >= 2)
            )
        ),

        -- CTE 11: 按总购买次数分组统计客户（根据分析类型采用不同的显示逻辑）
        RepurchaseStats AS (
            SELECT
                fctp.total_purchase_times,
                COUNT(fctp.客户唯一编码) as customer_count,
                SUM(fctp.customer_total_quantity) as total_quantity,
                SUM(fctp.customer_total_amount) as total_amount,
                AVG(fctp.customer_avg_interval) as avg_interval,
                cpa.is_cross_period
            FROM FilteredCustomerTotalPurchases fctp
            CROSS JOIN CrossPeriodAnalysis cpa
            GROUP BY fctp.total_purchase_times, cpa.is_cross_period
        )

        -- 最终查询: 基于分析类型采用不同的复购次数显示逻辑
        SELECT
            CASE
                -- 跨时间段分析：第N次 = 在回购期间购买了N次的客户数量
                WHEN rs.is_cross_period = 1 THEN CONCAT('第', rs.total_purchase_times, '次')
                -- 同时间段分析：第N次 = 购买了N+1次的客户数量（第N次复购）
                ELSE CONCAT('第', rs.total_purchase_times - 1, '次')
            END as repurchaseTimes,
            rs.customer_count as repurchaseCustomers,
            rs.total_quantity as repurchaseQuantity,
            ROUND(rs.total_amount, 2) as repurchaseAmount,
            CASE
                WHEN rs.customer_count > 0
                THEN ROUND(rs.total_amount / rs.customer_count, 2)
                ELSE NULL
            END as unitPrice,
            ROUND(rs.avg_interval, 2) as avgRepurchaseCycleDays
        FROM RepurchaseStats rs

        UNION ALL

        -- 合计行：所有复购客户的总计
        SELECT
            '合计' as repurchaseTimes,
            COUNT(fctp.客户唯一编码) as repurchaseCustomers,
            SUM(fctp.customer_total_quantity) as repurchaseQuantity,
            ROUND(SUM(fctp.customer_total_amount), 2) as repurchaseAmount,
            CASE
                WHEN COUNT(fctp.客户唯一编码) > 0
                THEN ROUND(SUM(fctp.customer_total_amount) / COUNT(fctp.客户唯一编码), 2)
                ELSE NULL
            END as unitPrice,
            ROUND(AVG(fctp.customer_avg_interval), 2) as avgRepurchaseCycleDays
        FROM FilteredCustomerTotalPurchases fctp

        UNION ALL

        -- 人均行：平均每个复购客户的数据
        SELECT
            '人均' as repurchaseTimes,
            NULL as repurchaseCustomers,
            CASE
                WHEN COUNT(fctp.客户唯一编码) > 0
                THEN ROUND(SUM(fctp.customer_total_quantity) / COUNT(fctp.客户唯一编码), 2)
                ELSE NULL
            END as repurchaseQuantity,
            CASE
                WHEN COUNT(fctp.客户唯一编码) > 0
                THEN ROUND(SUM(fctp.customer_total_amount) / COUNT(fctp.客户唯一编码), 2)
                ELSE NULL
            END as repurchaseAmount,
            CASE
                WHEN COUNT(fctp.客户唯一编码) > 0
                THEN ROUND(SUM(fctp.customer_total_amount) / COUNT(fctp.客户唯一编码), 2)
                ELSE NULL
            END as unitPrice,
            ROUND(AVG(fctp.customer_avg_interval), 2) as avgRepurchaseCycleDays
        FROM FilteredCustomerTotalPurchases fctp

        ORDER BY
            CASE
                WHEN repurchaseTimes LIKE '第%次' THEN CAST(SUBSTRING(repurchaseTimes, 2, CHAR_LENGTH(repurchaseTimes) - 2) AS UNSIGNED)
                WHEN repurchaseTimes = '人均' THEN 998
                WHEN repurchaseTimes = '合计' THEN 999
                ELSE 0
            END
    </select>

    <!-- 下载复购明细的订单明细数据 -->
    <select id="downloadOrderDetail" resultType="net.lab1024.sa.admin.module.business.customer.domain.vo.CustomerLinkOrderDetailVO">
        -- 下载明细查询（基于用户提供的SQL逻辑）
        -- 此查询用于数据核对，会列出所有底层订单
        WITH
        -- CTE 1: 筛选基础订单，与主报告逻辑一致（付款日期范围）
        FilteredOrders AS (
            SELECT om.客户唯一编码, om.数量, om.已付, DATE(om.付款时间) as 付款日期
            FROM lirun.订单明细 om
            <where>
                om.平台货品ID IS NOT NULL AND om.平台货品ID != ''
                AND om.客户唯一编码 IS NOT NULL AND om.客户唯一编码 != ''
                AND om.付款时间 IS NOT NULL
                AND om.订单状态 != '线下退款'
                AND NOT (om.订单来源 = '手工创建' AND om.订单状态 = '已取消')
                AND om.标记名称 != '贴贴贴贴贴'
                <if test="queryForm.excludeFlag != null and queryForm.excludeFlag != ''">
                    AND (om.客服标旗 IS NULL OR om.客服标旗 NOT IN
                    <foreach collection="queryForm.excludeFlag.split(',')" item="flag" open="(" separator="," close=")">
                        #{flag}
                    </foreach>)
                </if>
                <if test="queryForm.platformGoodsIds != null and queryForm.platformGoodsIds.size() > 0">
                    AND om.平台货品ID IN
                    <foreach collection="queryForm.platformGoodsIds" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="queryForm.startDate != null and queryForm.startDate != ''">
                    AND om.付款时间 &gt;= #{queryForm.startDate}
                </if>
                <if test="queryForm.endDate != null and queryForm.endDate != ''">
                    AND om.付款时间 &lt; DATE_ADD(#{queryForm.endDate}, INTERVAL 1 DAY)
                </if>
            </where>
        ),

        -- CTE 1.5: 回购日期期间内的客户筛选 - 只保留在回购日期期间内有购买行为的客户
        RepurchasePeriodCustomers AS (
            SELECT DISTINCT 客户唯一编码
            FROM lirun.订单明细 repurchase_order
            WHERE 1=1
            <if test="queryForm.platformGoodsIds != null and queryForm.platformGoodsIds.size() > 0">
                AND repurchase_order.平台货品ID IN
                <foreach collection="queryForm.platformGoodsIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="queryForm.repurchaseDateBegin != null and queryForm.repurchaseDateBegin != ''">
                AND repurchase_order.付款时间 &gt;= #{queryForm.repurchaseDateBegin}
            </if>
            <if test="queryForm.repurchaseDateEnd != null and queryForm.repurchaseDateEnd != ''">
                AND repurchase_order.付款时间 &lt; DATE_ADD(#{queryForm.repurchaseDateEnd}, INTERVAL 1 DAY)
            </if>
            AND repurchase_order.订单状态 != '线下退款'
            AND NOT (repurchase_order.订单来源 = '手工创建' AND repurchase_order.订单状态 = '已取消')
            AND repurchase_order.标记名称 != '贴贴贴贴贴'
            <if test="queryForm.excludeFlag != null and queryForm.excludeFlag != ''">
                AND (repurchase_order.客服标旗 IS NULL OR repurchase_order.客服标旗 NOT IN
                <foreach collection="queryForm.excludeFlag.split(',')" item="flag" open="(" separator="," close=")">
                    #{flag}
                </foreach>)
            </if>
        ),

        -- CTE 2: 基于lirun数据库实时计算客户分类，并应用回购日期筛选
        FilteredOrdersWithCustomerType AS (
            SELECT
                fo.客户唯一编码,
                fo.数量,
                fo.已付,
                fo.付款日期,
                CASE
                    WHEN EXISTS (
                        SELECT 1 FROM lirun.订单明细 hist
                        WHERE hist.客户唯一编码 = fo.客户唯一编码
                            AND hist.付款时间 &lt; #{queryForm.startDate}
                            AND hist.订单状态 != '线下退款'
                            AND NOT (hist.订单来源 = '手工创建' AND hist.订单状态 = '已取消')
                            AND hist.标记名称 != '贴贴贴贴贴'
                    ) THEN '老客'
                    ELSE '新客'
                END as 客户类型
            FROM FilteredOrders fo
            -- 只保留在回购日期期间内有购买行为的客户
            WHERE EXISTS (
                SELECT 1 FROM RepurchasePeriodCustomers rpc
                WHERE rpc.客户唯一编码 = fo.客户唯一编码
            )
            <if test="queryForm.customerType != null and queryForm.customerType != ''">
                AND CASE
                    WHEN EXISTS (
                        SELECT 1 FROM lirun.订单明细 hist
                        WHERE hist.客户唯一编码 = fo.客户唯一编码
                            AND hist.付款时间 &lt; #{queryForm.startDate}
                            AND hist.订单状态 != '线下退款'
                            AND NOT (hist.订单来源 = '手工创建' AND hist.订单状态 = '已取消')
                            AND hist.标记名称 != '贴贴贴贴贴'
                    ) THEN '老客'
                    ELSE '新客'
                END = #{queryForm.customerType}
            </if>
        ),
        -- CTE 2: 按天聚合，计算每日的购买事件
        DailyAggregatedPurchases AS (
            SELECT 客户唯一编码, 付款日期
            FROM FilteredOrdersWithCustomerType GROUP BY 客户唯一编码, 付款日期
        ),
        -- CTE 3: 计算每个客户的购买次序和总购买次数
        CustomerPurchaseStats AS (
            SELECT
                客户唯一编码,
                付款日期,
                ROW_NUMBER() OVER (PARTITION BY 客户唯一编码 ORDER BY 付款日期) as purchase_order,
                COUNT(*) OVER (PARTITION BY 客户唯一编码) as total_purchases
            FROM DailyAggregatedPurchases
        )
        -- 最终查询：将分析维度关联回原始的订单明细表
        SELECT
            -- 分析维度
            CASE
                WHEN cps.purchase_order = 1 THEN '首次购买'
                ELSE '复购'
            END as repurchaseTimes,
            cps.total_purchases as purchaseOrder,
            cps.purchase_order as repurchaseCycleDays,
            -- 原始订单明细
            od.客户唯一编码 as customerUniqueCode,
            od.原始单号 as originalOrderNo,
            od.平台货品ID as platformGoodsId,
            od.商家编码 as merchantCode,
            od.数量 as quantity,
            od.已付 as paidAmount,
            od.付款时间 as paymentTime,
            od.店铺名称 as shopName
        FROM
            lirun.订单明细 od
        JOIN
            CustomerPurchaseStats cps ON od.客户唯一编码 = cps.客户唯一编码 AND DATE(od.付款时间) = cps.付款日期
        <where>
            -- 再次应用核心过滤条件，确保关联的准确性
            od.订单状态 != '线下退款'
            AND NOT (od.订单来源 = '手工创建' AND od.订单状态 = '已取消')
            AND od.标记名称 != '贴贴贴贴贴'
            <if test="queryForm.platformGoodsIds != null and queryForm.platformGoodsIds.size() > 0">
                AND od.平台货品ID IN
                <foreach collection="queryForm.platformGoodsIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="queryForm.startDate != null and queryForm.startDate != ''">
                AND od.付款时间 &gt;= #{queryForm.startDate}
            </if>
            <if test="queryForm.endDate != null and queryForm.endDate != ''">
                AND od.付款时间 &lt; DATE_ADD(#{queryForm.endDate}, INTERVAL 1 DAY)
            </if>
            <if test="queryForm.excludeFlag != null and queryForm.excludeFlag != ''">
                AND (od.客服标旗 IS NULL OR od.客服标旗 NOT IN
                <foreach collection="queryForm.excludeFlag.split(',')" item="flag" open="(" separator="," close=")">
                    #{flag}
                </foreach>)
            </if>
        </where>
        ORDER BY
            od.客户唯一编码,
            od.付款时间
    </select>

</mapper>